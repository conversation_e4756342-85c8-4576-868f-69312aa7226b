:root {
  --primary-color: #4FB8A3;
  --secondary-color: #3A8A98;
  --accent-color: #2E5C6E;
  --background-color: #f8f9fe;
  --card-background: #FFFFFF;
  --text-primary: #2C3E50;
  --text-secondary: #5F6368;
  --success-color: #34A853;
  --warning-color: #FBBC05;
  --danger-color:#EA4335;
  --border-radius: 12px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Base responsive setup */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
}

body {
  overflow-x: hidden;
}

/* Responsive container improvements */
@media (min-width: 576px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 992px) {
  .container {
    padding: 0 2.5rem;
  }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
    background-image: url('/background.jpg');
    background-attachment: fixed;
    background-size: 100% 100%;
    background-color: var(--background-color);
    color: var(--text-primary);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.nav-bar {
  background: linear-gradient(135deg, #50a090 0%, #92fae5 100%);
  padding: 15px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 15px rgba(80, 160, 144, 0.3), 0 0 20px rgba(146, 250, 229, 0.2);
  color: white;
  min-height: 80px;
  position: relative;
  backdrop-filter: blur(10px);
}

.nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: nowrap;
}

.nav-links a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-links a:hover {
  background: linear-gradient(135deg, #62a093 0%, #7bc4b8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(98, 160, 147, 0.4), 0 0 15px rgba(98, 160, 147, 0.3);
  transform: translateY(-1px);
}

.nav-links a.active {
  background: linear-gradient(135deg, #62a093 0%, #7bc4b8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(98, 160, 147, 0.4), 0 0 15px rgba(98, 160, 147, 0.3);
  transform: translateY(-1px);
}

/* Mobile menu toggle button */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 0.25rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: white;
  transition: all 0.3s ease;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}

.location-dropdown {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 0.5rem 0.8rem;
  color: #2c3e50;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.location-dropdown:hover,
.location-dropdown:focus {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  outline: none;
}

.home-icon-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.home-icon-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.home-icon{
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
  filter: brightness(0) invert(1);
  transition: all 0.3s ease;
}

.home-icon-btn:hover .home-icon {
  filter: brightness(0) invert(1) drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

.comparison-location-bar {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}



/* Dashboard Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.metric-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: transform 0.2s ease;
  width: 100%;
  max-width: 280px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.metric-card:hover {
  transform: translateY(-3px);
}

.metric-card h3 {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  flex-shrink: 0;
}

.gauge-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gauge-container {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 1rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-badge {
  margin-top: 1rem;
}

.badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
}

.badge-good {
  background-color: var(--success-color);
  color: white;
}

.badge-moderate {
  background-color: var(--warning-color);
  color: white;
}

.badge-poor {
  background-color: var(--danger-color);
  color: white;
}

.metric-subtitle {
  margin-top: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Quality Scale Card */
.quality-scale-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  max-width: 1400px;
  margin: 2rem auto;
}

.quality-scale-card h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.scale-bar {
  height: 12px;
  background: linear-gradient(to right,
    var(--success-color) 0%,
    var(--warning-color) 50%,
    var(--danger-color) 100%
  );
  border-radius: 6px;
  margin: 1rem 0;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Container and Layout */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Welcome Section */
.welcome-section {
  background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(58, 138, 152, 0.1));
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-section h1 {
  color: var(--text-primary);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.welcome-section .subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  font-weight: 400;
}

/* Comparison Page Styles */
.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 2rem auto;
}

.location-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.location-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.location-card h3 {
  font-size: 1.5rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.location-card .gauge-container {
  width: 200px;
  height: 200px;
  margin: 1.5rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-card .status {
  text-align: center;
  margin: 1rem 0;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.chart-error {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 1rem;
  border-radius: var(--border-radius);
  text-align: center;
  margin: 2rem auto;
  max-width: 600px;
}

.comparison-summary {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem auto;
  max-width: 1200px;
}

.comparison-summary h3 {
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.comparison-text {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.daily-distribution {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 1rem auto 0.5rem auto;
  max-width: 1200px;
}

.daily-distribution h3 {
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

/* Hero Section */
.hero {
  background-image: url('/background.jpg');
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  color: var(--text-primary);
}

.hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Features Section */
.features {
  background: rgba(255, 255, 255, 0.95);
  padding: 4rem 2rem;
  margin: 2rem 0;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.container1 {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-item {
  padding: 2rem;
}

.feature-item h3 {
  color: var(--accent-color);
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.feature-item p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-top: 1.5rem;
}

/* Chart Container Improvements */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

/* Form Controls */
.form-select, .form-control {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(79, 184, 163, 0.25);
  outline: 0;
}

.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  transform: translateY(-1px);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  border: 1px solid transparent;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .nav-links {
    gap: 1rem;
  }

  .nav-links a {
    padding: 0.4rem 0.6rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 992px) {
  .nav-bar {
    padding: 15px 20px;
  }

  .nav-links {
    gap: 0.8rem;
  }

  .nav-links a {
    padding: 0.4rem 0.5rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .nav-bar {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    min-height: auto;
    position: relative;
  }

  .logo-container {
    order: 1;
    flex: 0 0 auto;
  }

  .mobile-menu-toggle {
    display: flex;
    order: 2;
    margin-left: auto;
  }

  .nav-links {
    order: 3;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #50a090 0%, #92fae5 100%);
    flex-direction: column;
    gap: 0;
    box-shadow: 0 4px 15px rgba(80, 160, 144, 0.3), 0 0 20px rgba(146, 250, 229, 0.2);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    backdrop-filter: blur(10px);
  }

  .nav-links.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links a {
    padding: 1rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .nav-links a:hover,
  .nav-links a.active {
    background: linear-gradient(135deg, #62a093 0%, #7bc4b8 100%);
    box-shadow: inset 0 2px 8px rgba(98, 160, 147, 0.3);
  }

  .nav-links a:last-child {
    border-bottom: none;
  }

  .comparison-location-bar, .search-container {
    order: 4;
    width: 100%;
    justify-content: center;
    margin-top: 1rem;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 0 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .welcome-section {
    padding: 2rem 1rem;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .nav-bar {
    padding: 0.8rem;
  }

  .nav-links a {
    padding: 0.8rem;
    font-size: 0.9rem;
  }

  .logo {
    height: 60px;
  }

  .mobile-menu-toggle {
    padding: 0.3rem;
  }

  .mobile-menu-toggle span {
    width: 20px;
    height: 2px;
  }
}

.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 50px;
  width: auto;
  border-radius: 8px;
}

.logo {
  height: 80px;
  border-radius: 10px
}

/* Duplicate nav-links styles removed - using main styles above */

/* Duplicate nav-links hover/active styles removed - using main styles above */

.auth-buttons {
  display: flex;
  gap: 1rem;
}

.user-info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #dbdddf;
  border-bottom: 1px solid #dee2e6;
}

.hero {
    text-align: center;
    padding: 50px 20px;
    background: url('/background.jpg') no-repeat center center/cover;
    color: #333;
}

.hero h1 {
    font-size: 50px;
    margin-bottom: 10px;
}

.hero h2 {
    font-size: 45px;
    font-weight: bold;
    color: #090909;
}

.hero p {
    font-size: 25px;
    margin-bottom: 20px;
}

/* About Us and Contact Us Page Styles */
.about-us-container,
.contact-us-container {
  min-height: 100vh;
}

/* New About Us Hero Section */
.about-hero-section {
  background-color: #f8f9fa;
  padding: 3rem 0 2rem 0;
  min-height: 50vh;
  display: flex;
  align-items: center;
}

.about-hero-section .breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
  font-size: 0.9rem;
}

.about-hero-section .breadcrumb-item a {
  color: #6c757d;
  text-decoration: none;
}

.about-hero-section .breadcrumb-item a:hover {
  color: var(--primary-color);
}

.about-hero-section .breadcrumb-item.active {
  color: #495057;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  line-height: 1.2;
}

/* About Content Section */
.about-content-section {
  padding: 4rem 0;
}

.content-text {
  padding-right: 2rem;
}

.content-text .lead {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.content-section p {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  text-align: justify;
}

/* Images Container */
.images-container {
  padding-left: 2rem;
}

.image-card img {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-card img:hover {
  transform: scale(1.02);
}

/* Dashboard Image Specific Styles */
.main-dashboard {
  margin-bottom: 1rem;
}

.dashboard-image {
  border: 2px solid #e9ecef;
  border-radius: 12px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease;
}

.dashboard-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15) !important;
}

.image-caption {
  text-align: center;
  font-style: italic;
}

/* Feature Highlight Cards */
.feature-highlight {
  height: 100%;
  transition: transform 0.3s ease;
  border: 1px solid #dee2e6;
}

.feature-highlight:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-highlight h5 {
  font-size: 1rem;
  font-weight: 600;
}

.feature-highlight ul li {
  margin-bottom: 0.25rem;
  font-size: 0.85rem;
}

.secondary-image {
  height: 100%;
}

/* Additional Info Section */
.about-additional-info {
  background-color: #f8f9fa;
}

.about-additional-info .section-title {
  color: var(--primary-color);
}

/* Legacy styles for backward compatibility */
.about-hero,
.contact-hero {
  background: linear-gradient(135deg, var(--primary-color), #5ba89b);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.about-hero h1,
.contact-hero h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.about-hero .lead,
.contact-hero .lead {
  font-size: 1.25rem;
  opacity: 0.9;
}

.contact-info .contact-item {
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
}

.contact-info .contact-item:last-child {
  border-bottom: none;
}

.contact-info h5 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.contact-info i {
  margin-right: 0.5rem;
}

.contact-form .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 0.75rem 2rem;
}

.contact-form .btn-primary:hover {
  background-color: #5ba89b;
  border-color: #5ba89b;
}

/* Responsive styles for About Us and Contact Us pages */
@media (max-width: 768px) {
  /* New About Us responsive styles */
  .about-hero-section {
    padding: 2rem 0 1.5rem 0;
    min-height: 40vh;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .about-content-section {
    padding: 2rem 0;
  }

  .content-text {
    padding-right: 0;
    margin-bottom: 2rem;
  }

  .images-container {
    padding-left: 0;
    margin-top: 2rem;
  }

  .main-dashboard {
    margin-bottom: 1.5rem;
  }

  .dashboard-image {
    border-radius: 8px !important;
  }

  .feature-highlight {
    margin-bottom: 1rem;
  }

  .feature-highlight h5 {
    font-size: 0.9rem;
  }

  .feature-highlight ul li {
    font-size: 0.8rem;
  }

  .content-text .lead {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .about-additional-info {
    padding: 2rem 0;
  }

  /* Legacy responsive styles */
  .about-hero,
  .contact-hero {
    padding: 2rem 0;
  }

  .about-hero h1,
  .contact-hero h1 {
    font-size: 2rem;
  }

  .about-hero .lead,
  .contact-hero .lead {
    font-size: 1.1rem;
  }

  .contact-info .contact-item {
    padding: 0.8rem 0;
  }

  .contact-form {
    margin-top: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .about-hero-section .breadcrumb {
    font-size: 0.8rem;
  }

  .content-text .lead {
    font-size: 0.95rem;
  }

  .section-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 576px) {
  .about-hero,
  .contact-hero {
    padding: 1.5rem 0;
  }

  .about-hero h1,
  .contact-hero h1 {
    font-size: 1.8rem;
  }

  .about-hero .lead,
  .contact-hero .lead {
    font-size: 1rem;
  }
}

.real-time-monitoring {
    margin: 20px auto;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    max-width: 400px;
    border-radius: 8px;
}

footer {
    text-align: center;
    padding: 20px 0;
    background-color: #f1f1f1;
    color: #555;
    font-size: 16px;
}

.logo {
    height: 80px;
}

.features {
    background-color: #dfdede;
    padding: 2rem 1rem;
}

.features h2 {
    font-family: 'Poppins', sans-serif;
    font-size:40px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.feature-item {
    background-color: #F7F7F7;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.feature-item h3 {
    font-weight:bold ;
    font-family: 'Poppins', sans-serif;
    font-size: 25px;
    color: #2A3E59;
    margin-bottom: 0.5rem;
}

.feature-item p {
    font-size: 20px;
    color: #555555;
}

.carousel-indicators button {
    background-color: #32B3A4;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: #32B3A4;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-social-icons {
    margin-top: 1rem;
}

.footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
    gap: 300px;
}

.container {
  background-color: #FFFFFF;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  text-align: center;
}

.input-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #2A3E59;
  margin-bottom: 0.5rem;
}

.input-group input, .input-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #32B3A4;
  border-radius: 5px;
  font-size: 1rem;
}

.input-group input:focus, .input-group select:focus {
  outline: none;
  border-color: #27988A;
  box-shadow: 0 0 5px rgba(50, 179, 164, 0.5);
}

.btn-primary {
  display: inline-block;
  background-color: #32B3A4;
  color: #FFFFFF;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: #27988A;
}

.hidden {
  display: none;
}

.view {
  display: block;
}

/* Dashboard Styles */
.welcome-section {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(79, 184, 163, 0.1), rgba(46, 92, 110, 0.1));
}

.welcome-section h1 {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.welcome-section .subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary);
}

/* Main container for dashboard and comparison */
main.container, main.dashboard-container {
  width: 100%;
  max-width: 95%;
  margin: 0 auto;
  padding: 1rem;
  box-sizing: border-box;
}

.quality-scale-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  max-width: 1400px;
  margin: 2rem auto;
}

.quality-scale-card h2 {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.scale-bar {
  height: 8px;
  background: linear-gradient(to right,
      #34A853,  /* Good - Green */
      #FBBC05,  /* Moderate - Yellow */
      #EA4335   /* Poor - Red */
  );
  border-radius: 4px;
  margin: 1rem 0;
}

.scale-labels {
  display: flex;
  justify-content: space-between;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}





.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 1rem 0;
}

.metric-status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.8rem;
}

.status-good {
  background-color: var(--success-color);
  color: white;
}

.status-moderate {
  background-color: var(--warning-color);
  color: white;
}

.status-poor {
  background-color: var(--danger-color);
  color: white;
}

.trends-section {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem 0;
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.trends-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.chart-container {
  height: 400px;
  position: relative;
}



.location-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.location-card h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.location-select, .date-picker input, .metrics-select select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.gauge-container {
  text-align: center;
  margin: 2rem 0;
  position: relative;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.value-display {
  font-size: 3rem;
  font-weight: bold;
  color: var(--text-primary);
}

.status {
  text-align: center;
  margin: 1rem 0;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.good {
  background-color: var(--success-color);
}

.dot.moderate {
  background-color: var(--warning-color);
}

.dot.poor {
  background-color: var(--danger-color);
}

.comparison-summary {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 2rem 0;
}

.comparison-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}



.validation-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  border: 1px solid #f5c6cb;
}

.chart-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  text-align: center;
}

/* Location dropdown styles */
.comparison-location-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.location-dropdown:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Additional Dashboard Styles */
.metric-card-hover:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

.refresh-button {
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: rotate(180deg);
}

/* AQI specific styling */
.aqi-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.aqi-card .card-title {
  color: rgba(255, 255, 255, 0.9);
}

/* Status indicators */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -15px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Comparison page enhancements */
.location-card {
  transition: box-shadow 0.3s ease;
}

.location-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}



/* Chart container improvements */
.chart-container {
  background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* Loading animations */
.loading-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Enhanced buttons */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 184, 163, 0.3);
}

/* Additional responsive styles for specific components */
@media (max-width: 768px) {
  .hero h2 {
    font-size: 1.8rem;
  }

  .hero p {
    font-size: 1.2rem;
  }

  .trends-header {
    flex-direction: column;
    align-items: stretch;
  }

  .trends-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .gauge-container {
    width: 150px;
    height: 150px;
  }
}
