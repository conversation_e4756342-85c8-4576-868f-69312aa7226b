:root {
    --primary-color: #4FB8A3;
    --secondary-color: #3A8A98;
    --accent-color: #2E5C6E;
    --background-color: #f8f9fe;
    --card-background: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #5F6368;
    --success-color: #34A853;
    --warning-color: #FBBC05;
    --danger-color: #EA4335;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.container h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #011c40;
    font-size: 2rem;
}

.validation-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
    border-radius: 0.375rem;
    border: 1px solid #f5c6cb;
    text-align: center;
    font-weight: 500;
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    justify-items: center;
    align-items: start;
}

.location-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease;
    width: 100%;
    max-width: 600px;
}

.location-card:hover {
    transform: translateY(-2px);
}

.location-card h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #011c40;
    font-size: 1.5rem;
    font-weight: 600;
}

.location-select, .date-picker input, .metrics-select select {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.2s ease;
}

.location-select:focus, .date-picker input:focus, .metrics-select select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 184, 163, 0.2);
}

.date-picker, .metrics-select {
    margin-bottom: 1.5rem;
}

.date-picker label, .metrics-select label {
    color: #011c40;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.date-picker label, .metrics-select label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.gauge-container {
    text-align: center;
    margin: 2.5rem 0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 280px;
}

.gauge-container canvas {
    max-width: 220px;
    height: 220px !important;
    margin-bottom: 1rem;
}

.value-display {
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 0.5rem;
    color: var(--text-primary);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.status {
    text-align: center;
    padding: 0.5rem;
    margin: 1rem 0;
    border-radius: 6px;
    font-weight: 500;
    text-transform: capitalize;
}

.status.good {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status.moderate {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status.poor {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.legend-item .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.legend-item .dot.good {
    background-color: var(--success-color);
}

.legend-item .dot.moderate {
    background-color: var(--warning-color);
}

.legend-item .dot.poor {
    background-color: var(--danger-color);
}

.legend-item .dot.moderate {
    background-color: var(--warning-color);
}

.chart-error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 0.375rem;
    border: 1px solid #f5c6cb;
    text-align: center;
}

.comparison-summary {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    margin: 2rem 0;
    width: 100%;
    text-align: center;
    align-self: center;
}

.comparison-summary h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.comparison-text {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}

.daily-distribution {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    margin: 2rem 0;
    width: 100%;
    align-self: center;
}

.daily-distribution h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.chart-container {
    position: relative;
    height: 450px;
    width: 100%;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

/* Responsive Design */
@media (min-width: 1200px) {
    .comparison-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 1000px;
    }
}

@media (max-width: 1199px) and (min-width: 768px) {
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        max-width: 500px;
    }

    .location-card {
        max-width: 100%;
    }

    .gauge-container canvas {
        max-width: 200px;
        height: 200px !important;
    }
}

@media (max-width: 767px) {
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 100%;
        padding: 0 1rem;
    }

    .container {
        padding: 1rem;
    }

    .location-card {
        padding: 1.5rem;
        max-width: 100%;
    }

    .gauge-container {
        min-height: 220px;
    }

    .gauge-container canvas {
        max-width: 180px;
        height: 180px !important;
    }

    .chart-container {
        height: 300px;
        padding: 1rem;
    }

    .comparison-summary,
    .daily-distribution {
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .legend {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .welcome-section {
        padding: 2rem 1rem;
    }

    .welcome-section h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }

    .container h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .location-card {
        padding: 1rem;
    }

    .location-card h3 {
        font-size: 1.25rem;
    }

    .gauge-container {
        min-height: 200px;
        margin: 1.5rem 0;
    }

    .gauge-container canvas {
        max-width: 150px;
        height: 150px !important;
    }

    .value-display {
        font-size: 1rem;
    }

    .chart-container {
        height: 250px;
        padding: 0.5rem;
    }

    .comparison-summary,
    .daily-distribution {
        padding: 1rem;
        margin: 1rem 0;
    }

    .welcome-section h1 {
        font-size: 1.75rem;
    }

    .welcome-section .subtitle {
        font-size: 0.9rem;
    }
}

.welcome-section {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--card-background);
    margin-bottom: 2rem;
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-section .subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
}
