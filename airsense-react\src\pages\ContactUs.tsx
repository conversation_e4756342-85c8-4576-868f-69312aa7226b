import React, { useState } from 'react';
import Layout from '../layouts/Layout';

const ContactUs: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We will get back to you soon.');
    // Reset form
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
  };

  return (
    <Layout showNavbar={true}>
      <div className="contact-us-container">
        <main className="container my-5">
          <div className="row">
            <div className="col-lg-8 mx-auto">
              <div className="row">
                <div className="col-md-6">
                  <section className="mb-5">
                    <h2>Get In Touch</h2>
                    <p>
                      Have questions about AirSense or need support? We'd love to hear from you. 
                      Send us a message and we'll respond as soon as possible.
                    </p>
                    
                    <div className="contact-info mt-4">
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-envelope"></i> Email</h5>
                        <p><EMAIL></p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-telephone"></i> Phone</h5>
                        <p>+****************</p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-geo-alt"></i> Address</h5>
                        <p>
                          123 Tech Street<br />
                          Innovation District<br />
                          Tech City, TC 12345
                        </p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-clock"></i> Business Hours</h5>
                        <p>
                          Monday - Friday: 9:00 AM - 6:00 PM<br />
                          Saturday: 10:00 AM - 4:00 PM<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </section>
                </div>
                
                <div className="col-md-6">
                  <section className="mb-5">
                    <h2>Send us a Message</h2>
                    <form onSubmit={handleSubmit} className="contact-form">
                      <div className="mb-3">
                        <label htmlFor="name" className="form-label">Name *</label>
                        <input
                          type="text"
                          className="form-control"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="email" className="form-label">Email *</label>
                        <input
                          type="email"
                          className="form-control"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="subject" className="form-label">Subject *</label>
                        <input
                          type="text"
                          className="form-control"
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="message" className="form-label">Message *</label>
                        <textarea
                          className="form-control"
                          id="message"
                          name="message"
                          rows={5}
                          value={formData.message}
                          onChange={handleInputChange}
                          required
                        ></textarea>
                      </div>
                      
                      <button type="submit" className="btn btn-primary">
                        Send Message
                      </button>
                    </form>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
};

export default ContactUs;
