<!DOCTYPE html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirSense - Indoor Air Quality Monitoring</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            min-height: 100vh;
        }

        /* LEFT SECTION */
        .left-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .main-title {
            font-size: 2.5rem;
            color: #2c5aa0;
            font-weight: bold;
            line-height: 1.2;
            margin-bottom: 20px;
        }

        .left-image {
            width: 100%;
            height: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        .image-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c5aa0;
            font-size: 1.1rem;
            text-align: center;
        }

        .mission-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .mission-card h2 {
            color: #2c5aa0;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .mission-card p {
            color: #666;
            font-size: 1rem;
            line-height: 1.8;
        }

        .air-quality-matters {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .air-quality-matters h2 {
            color: #2c5aa0;
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .air-quality-matters p {
            color: #666;
            font-size: 1rem;
            line-height: 1.8;
        }

        .dashboard-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            text-align: center;
        }

        .dashboard-section h2 {
            color: #2c5aa0;
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .dashboard-placeholder {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            height: 200px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2c5aa0;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .dashboard-subtitle {
            color: #666;
            font-size: 1rem;
            font-style: italic;
        }

        /* RIGHT SECTION */
        .right-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
            padding-top: 80px;
        }

        .right-image {
            width: 100%;
            height: 250px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        }

        .welcome-card h2 {
            font-size: 1.8rem;
            margin-bottom: 15px;
        }

        .welcome-card p {
            font-size: 1rem;
            line-height: 1.8;
        }

        .what-we-do {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .what-we-do h2 {
            color: #2c5aa0;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .what-we-do p {
            color: #666;
            font-size: 1rem;
            line-height: 1.8;
        }

        .monitoring-alerts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .monitoring-card,
        .alerts-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .monitoring-card h3,
        .alerts-card h3 {
            color: #2c5aa0;
            font-size: 1.4rem;
            margin-bottom: 15px;
        }

        .monitoring-card ul {
            list-style: none;
            padding-left: 0;
        }

        .monitoring-card li {
            color: #666;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .monitoring-card li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        .alerts-card p {
            color: #666;
            font-size: 1rem;
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 30px;
                padding: 20px 15px;
            }
            
            .right-section {
                padding-top: 0;
            }
            
            .main-title {
                font-size: 2rem;
            }
            
            .monitoring-alerts {
                grid-template-columns: 1fr;
            }
        }

        /* Hover Effects */
        .left-image:hover,
        .right-image:hover,
        .mission-card:hover,
        .welcome-card:hover,
        .what-we-do:hover,
        .air-quality-matters:hover,
        .monitoring-card:hover,
        .alerts-card:hover,
        .dashboard-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 32px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- LEFT SECTION -->
        <div class="left-section">
            <h1 class="main-title">AirSense Is Best Choice For You</h1>
            
            <div class="left-image">
                <div class="image-placeholder">
                    City Pollution<br>Monitoring Image
                </div>
            </div>

            <div class="mission-card">
                <h2>Our Mission</h2>
                <p>At AirSense, we are dedicated to creating healthier indoor environments through advanced air quality monitoring technology. Our mission is to provide real-time, accurate air quality data that empowers individuals and organizations to make informed decisions about their indoor air quality.</p>
            </div>

            <div class="air-quality-matters">
                <h2>Why Air Quality Matters</h2>
                <p>Indoor air quality significantly impacts our health, productivity, and overall well-being. Poor air quality can lead to various health issues including respiratory problems, allergies, and reduced cognitive function. Our system helps you identify potential issues, monitor trends, make data-driven decisions, and create healthier spaces for work, study, and living.</p>
            </div>

            <div class="dashboard-section">
                <h2>Indoor Air Quality Dashboard</h2>
                <div class="dashboard-placeholder">
                    Real-Time Air Quality<br>Monitoring Dashboard
                </div>
                <p class="dashboard-subtitle">Real-Time Air Quality Monitoring Dashboard</p>
            </div>
        </div>

        <!-- RIGHT SECTION -->
        <div class="right-section">
            <div class="right-image">
                <div class="image-placeholder">
                    Healthcare Professional<br>Using Air Quality Monitor
                </div>
            </div>

            <div class="welcome-card">
                <h2>Welcome!</h2>
                <p>We're passionate about clean air and committed to sharing the benefits of healthy indoor environments. Our page is where you'll discover our love for air quality monitoring, our mission to connect people with better air, and our dedication to fostering a vibrant community of health-conscious individuals.</p>
            </div>

            <div class="what-we-do">
                <h2>What We Do</h2>
                <p>AirSense offers a comprehensive indoor air quality monitoring system that tracks multiple environmental parameters including AQI measurements, temperature and humidity monitoring, real-time data visualization, historical data analysis, and location-based monitoring across different areas.</p>
            </div>

            <div class="monitoring-alerts">
                <div class="monitoring-card">
                    <h3>Multi-Parameter Monitoring</h3>
                    <ul>
                        <li>Air Quality Index (AQI)</li>
                        <li>Temperature & Humidity</li>
                        <li>CO₂ & PM2.5 Levels</li>
                        <li>Gas Resistance & Pressure</li>
                    </ul>
                </div>

                <div class="alerts-card">
                    <h3>Real-Time Alerts</h3>
                    <p>Get instant notifications when air quality parameters exceed safe levels.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Dashboard interaction
            const dashboard = document.querySelector('.dashboard-placeholder');
            dashboard.addEventListener('click', function() {
                this.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                this.style.color = 'white';
                this.innerHTML = 'Loading Dashboard...';
                
                setTimeout(() => {
                    this.style.background = 'linear-gradient(135deg, #e3f2fd, #bbdefb)';
                    this.style.color = '#2c5aa0';
                    this.innerHTML = 'Real-Time Air Quality<br>Monitoring Dashboard';
                }, 2000);
            });

            // Smooth scroll effects
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                
                document.querySelector('.main-title').style.transform = `translateY(${rate}px)`;
            });
        });
    </script>
</body>
</html>