import React from 'react';
import Layout from '../layouts/Layout';

const AboutUs: React.FC = () => {
  return (
    <Layout showNavbar={true}>
      <div className="about-us-container">
        {/* Hero Section */}
        <section className="about-hero-section">
          <div className="container">
            <div className="row align-items-center min-vh-50">
              <div className="col-12 text-center">
                <nav aria-label="breadcrumb" className="mb-4">
                  <ol className="breadcrumb justify-content-center">
                    <li className="breadcrumb-item">
                      <a href="/" className="text-decoration-none">Home</a>
                    </li>
                    <li className="breadcrumb-item active" aria-current="page">About us</li>
                  </ol>
                </nav>
                <h1 className="hero-title">AirSense Is Best Choice For You</h1>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="about-content-section py-5">
          <div className="container">
            <div className="row align-items-center">
              {/* Left Content */}
              <div className="col-lg-6 col-md-12 mb-4 mb-lg-0">
                <div className="content-text">
                  <p className="lead mb-4">
                    Welcome! We're passionate about clean air and committed to sharing the benefits of healthy indoor environments. Our page is where you'll discover our love for air quality monitoring, our mission to connect people with better air, and our dedication to fostering a vibrant community of health-conscious individuals.
                  </p>

                  <div className="content-section mb-4">
                    <h3 className="section-title">Our Mission</h3>
                    <p>
                      At AirSense, we are dedicated to creating healthier indoor environments through
                      advanced air quality monitoring technology. Our mission is to provide real-time,
                      accurate air quality data that empowers individuals and organizations to make
                      informed decisions about their indoor air quality.
                    </p>
                  </div>

                  <div className="content-section mb-4">
                    <h3 className="section-title">What We Do</h3>
                    <p>
                      AirSense offers a comprehensive indoor air quality monitoring system that tracks
                      multiple environmental parameters including AQI measurements, temperature and humidity monitoring,
                      real-time data visualization, historical data analysis, and location-based monitoring across different areas.
                    </p>
                  </div>

                  <div className="content-section mb-4">
                    <h3 className="section-title">Why Air Quality Matters</h3>
                    <p>
                      Indoor air quality significantly impacts our health, productivity, and overall
                      well-being. Poor air quality can lead to various health issues including
                      respiratory problems, allergies, and reduced cognitive function. Our system
                      helps you identify potential issues, monitor trends, make data-driven decisions,
                      and create healthier spaces for work, study, and living.
                    </p>
                  </div>
                </div>
              </div>

              {/* Right Images */}
              <div className="col-lg-6 col-md-12">
                <div className="images-container">
                  <div className="row g-3">
                    <div className="col-6">
                      <div className="image-card">
                        <img
                          src="/api/placeholder/300/400"
                          alt="Air Quality Monitor Device"
                          className="img-fluid rounded shadow-sm"
                        />
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="image-card mt-4">
                        <img
                          src="/api/placeholder/300/400"
                          alt="Indoor Air Quality Dashboard"
                          className="img-fluid rounded shadow-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Additional Information Section */}
        <section className="about-additional-info py-5 bg-light">
          <div className="container">
            <div className="row">
              <div className="col-lg-8 mx-auto">
                <div className="content-section mb-4">
                  <h3 className="section-title text-center mb-4">Our Technology</h3>
                  <p className="text-center">
                    Built with cutting-edge sensor technology and modern web frameworks, AirSense
                    provides accurate, real-time monitoring with an intuitive user interface. Our
                    system features high-precision environmental sensors, real-time data processing,
                    multi-location monitoring capabilities, historical data storage, and user-friendly
                    dashboard and comparison tools.
                  </p>
                </div>

                <div className="content-section">
                  <h3 className="section-title text-center mb-4">Our Commitment</h3>
                  <p className="text-center">
                    We are committed to continuous improvement and innovation in air quality
                    monitoring technology. Our team works tirelessly to ensure that AirSense
                    remains at the forefront of indoor environmental monitoring, providing you
                    with the most accurate and actionable data possible.
                  </p>
                  <p className="text-center">
                    <strong>Together, we can create healthier indoor environments for everyone.</strong>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default AboutUs;
